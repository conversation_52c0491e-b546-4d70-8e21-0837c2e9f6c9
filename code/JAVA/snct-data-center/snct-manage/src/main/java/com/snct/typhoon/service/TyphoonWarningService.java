package com.snct.typhoon.service;

import com.snct.common.domain.TyphoonShipWarning;
import com.snct.common.enums.TyphoonWarningLevel;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Ship;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IShipService;
import com.snct.typhoon.domain.vo.ActiveTyphoonSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResult;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 台风预警服务
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class TyphoonWarningService {
    
    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningService.class);
    
    /**
     * 预警去重时间窗口（毫秒）- 1小时
     */
    private static final long WARNING_DEDUP_WINDOW = 3600000L;
    
    @Autowired
    private IBuShipWarningService buShipWarningService;
    
    @Autowired
    private IShipService shipService;
    
    @Autowired
    private RedisTemplate redisTemplate;
    
    /**
     * 执行台风预警检查并保存到数据库
     */
    public void checkAndSaveTyphoonWarnings() {
        try {
            logger.info("开始执行台风预警检查");
            
            // 1. 获取活跃台风
            List<ActiveTyphoonSummary> activeTyphoons = getActiveTyphoons();
            
            if (activeTyphoons.isEmpty()) {
                logger.info("当前无活跃台风，跳过预警检查");
                return;
            }
            
            logger.info("发现 {} 个活跃台风", activeTyphoons.size());
            
            // 2. 对每个台风进行预警检查
            int totalWarnings = 0;
            for (ActiveTyphoonSummary typhoon : activeTyphoons) {
                try {
                    List<TyphoonShipWarning> warnings = checkTyphoonWarnings(typhoon);
                    
                    if (!warnings.isEmpty()) {
                        // 3. 保存预警记录到数据库
                        int savedCount = saveTyphoonWarnings(warnings, typhoon);
                        totalWarnings += savedCount;
                        logger.info("台风 {} 触发 {} 条预警，成功保存 {} 条", 
                                   typhoon.getName(), warnings.size(), savedCount);
                    } else {
                        logger.debug("台风 {} 未触发预警", typhoon.getName());
                    }
                } catch (Exception e) {
                    logger.error("处理台风 {} 预警时发生错误", typhoon.getName(), e);
                }
            }
            
            logger.info("台风预警检查完成，共处理 {} 个台风，生成 {} 条预警记录", 
                       activeTyphoons.size(), totalWarnings);
            
        } catch (Exception e) {
            logger.error("台风预警检查失败", e);
        }
    }
    
    /**
     * 检查单个台风的预警情况
     */
    public List<TyphoonShipWarning> checkTyphoonWarnings(ActiveTyphoonSummary typhoon) {
        List<TyphoonShipWarning> warnings = new ArrayList<>();
        
        try {
            // 验证台风坐标
            if (!StringUtils.hasText(typhoon.getCenterLng()) || 
                !StringUtils.hasText(typhoon.getCenterLat())) {
                logger.warn("台风 {} 坐标信息不完整，跳过预警检查", typhoon.getName());
                return warnings;
            }
            
            double typhoonLng = Double.parseDouble(typhoon.getCenterLng());
            double typhoonLat = Double.parseDouble(typhoon.getCenterLat());
            
            // 使用GEORADIUS查询最大预警半径内的船舶
            Point typhoonCenter = new Point(typhoonLng, typhoonLat);
            
            // 使用Redis GEO命令查询预警范围内的船舶
            GeoOperations<String, String> geoOps = redisTemplate.opsForGeo();

            // 创建距离对象
            Distance maxDistance = new Distance(TyphoonWarningLevel.getMaxRadius(), Metrics.KILOMETERS);

            // 执行GEORADIUS查询
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;
            GeoResults<RedisGeoCommands.GeoLocation<String>> results =
                geoOps.radius(
                    geoKey,
                    typhoonCenter,
                    maxDistance,
                    RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                        .includeDistance()
                        .includeCoordinates()
                        .sortAscending()
                );
            
            if (results == null || results.getContent().isEmpty()) {
                logger.debug("台风 {} 预警范围内未发现船舶", typhoon.getName());
                return warnings;
            }
            
            logger.debug("台风 {} 预警范围内发现 {} 艘船舶", typhoon.getName(), results.getContent().size());
            
            // 处理查询结果
            for (GeoResult<RedisGeoCommands.GeoLocation<String>> result : results) {
                try {
                    String shipSn = result.getContent().getName();
                    double distance = result.getDistance().getValue();
                    Point shipLocation = result.getContent().getPoint();
                    
                    // 确定预警级别
                    TyphoonWarningLevel warningLevel = TyphoonWarningLevel.determineByDistance(distance);
                    if (warningLevel != null) {
                        TyphoonShipWarning warning = new TyphoonShipWarning(
                            shipSn, null, distance, warningLevel, shipLocation,
                            typhoon.getTfid(), typhoon.getName(), typhoonCenter
                        );
                        warnings.add(warning);
                        
                        logger.debug("船舶 {} 触发 {} 级预警，距离 {:.1f}km", 
                                   shipSn, warningLevel.getDescription(), distance);
                    }
                } catch (Exception e) {
                    logger.error("处理船舶预警信息时发生错误", e);
                }
            }
            
        } catch (Exception e) {
            logger.error("检查台风 {} 预警时发生错误", typhoon.getName(), e);
        }
        
        return warnings;
    }

    /**
     * 保存台风预警记录到数据库
     */
    public int saveTyphoonWarnings(List<TyphoonShipWarning> warnings, ActiveTyphoonSummary typhoon) {
        int savedCount = 0;

        for (TyphoonShipWarning warning : warnings) {
            try {
                // 检查是否已存在相同的预警记录（避免重复预警）
                if (isWarningAlreadyExists(warning.getShipSn(), typhoon.getTfid(),
                                         warning.getLevel())) {
                    logger.debug("船舶 {} 的预警记录已存在，跳过保存", warning.getShipSn());
                    continue;
                }

                // 获取船舶信息
                Ship ship = shipService.selectShipByShipSn(warning.getShipSn());
                if (ship == null) {
                    logger.warn("未找到船舶信息: {}", warning.getShipSn());
                    continue;
                }

                // 设置船舶名称
                warning.setShipName(ship.getName());

                // 构建预警记录
                BuShipWarning warningRecord = createWarningRecord(warning, ship, typhoon);

                // 保存到数据库
                buShipWarningService.insertBuShipWarning(warningRecord);
                savedCount++;

                logger.info("保存台风预警记录: 船舶={}({}), 台风={}, 距离={:.1f}km, 级别={}",
                           ship.getName(), warning.getShipSn(), typhoon.getName(),
                           warning.getDistance(), warning.getLevel().getDescription());

            } catch (Exception e) {
                logger.error("保存预警记录失败: 船舶={}, 台风={}",
                           warning.getShipSn(), typhoon.getName(), e);
            }
        }

        return savedCount;
    }

    /**
     * 创建预警记录对象
     */
    private BuShipWarning createWarningRecord(TyphoonShipWarning warning, Ship ship,
                                            ActiveTyphoonSummary typhoon) {
        BuShipWarning warningRecord = new BuShipWarning();

        // 基本信息
        warningRecord.setType(1L); // 台风预警
        warningRecord.setLevel((long) warning.getLevel().getDbLevel());
        warningRecord.setSn(warning.getShipSn());
        warningRecord.setDeptId(ship.getDeptId());
        warningRecord.setStatus(0L); // 未处理

        // 预警信息
        warningRecord.setName(warning.generateWarningMessage());

        // 数据键（存储台风和位置相关信息）
        warningRecord.setDataKey(warning.generateDataKey());

        // 备注信息
        String remark = String.format(
            "台风强度:%s, 移动方向:%s, 移动速度:%s, 风力等级:%s",
            typhoon.getStrong() != null ? typhoon.getStrong() : "未知",
            typhoon.getMovedirection() != null ? typhoon.getMovedirection() : "未知",
            typhoon.getMovespeed() != null ? typhoon.getMovespeed() : "未知",
            typhoon.getPower() != null ? typhoon.getPower() : "未知"
        );
        warningRecord.setRemark(remark);

        return warningRecord;
    }

    /**
     * 检查预警是否已存在（防重复）
     */
    private boolean isWarningAlreadyExists(String shipSn, String typhoonId,
                                         TyphoonWarningLevel level) {
        try {
            // 查询最近时间窗口内是否已有相同的预警记录
            BuShipWarning queryCondition = new BuShipWarning();
            queryCondition.setSn(shipSn);
            queryCondition.setType(1L); // 台风预警
            queryCondition.setLevel((long) level.getDbLevel());

            List<BuShipWarning> existingWarnings =
                buShipWarningService.selectBuShipWarningList(queryCondition);

            // 检查是否有时间窗口内的相同台风预警
            Date windowStart = new Date(System.currentTimeMillis() - WARNING_DEDUP_WINDOW);
            return existingWarnings.stream()
                .anyMatch(w -> w.getCreateTime() != null &&
                              w.getCreateTime().after(windowStart) &&
                              w.getDataKey() != null &&
                              w.getDataKey().contains("TYPHOON:" + typhoonId));

        } catch (Exception e) {
            logger.error("检查预警重复性时发生错误: 船舶={}, 台风={}", shipSn, typhoonId, e);
            return false; // 出错时允许创建预警，避免漏报
        }
    }

    /**
     * 获取活跃台风列表
     */
    @SuppressWarnings("unchecked")
    private List<ActiveTyphoonSummary> getActiveTyphoons() {
        try {
            Map<String, Object> activeTyphoonData =
                (Map<String, Object>) redisTemplate.opsForValue()
                    .get(RedisParameter.TYPHOON_ACTIVE_SUMMARY);

            if (activeTyphoonData != null && activeTyphoonData.containsKey("typhoons")) {
                Object typhoonsObj = activeTyphoonData.get("typhoons");
                if (typhoonsObj instanceof List) {
                    return (List<ActiveTyphoonSummary>) typhoonsObj;
                }
            }
        } catch (Exception e) {
            logger.error("获取活跃台风列表时发生错误", e);
        }

        return new ArrayList<>();
    }

    /**
     * 获取指定台风的预警统计信息
     */
    public Map<String, Object> getTyphoonWarningStatistics(String typhoonId) {
        // 可以在这里实现预警统计功能
        // 返回各级别预警数量、影响船舶数量等统计信息
        return null;
    }

    /**
     * 清理过期的预警记录
     */
    public void cleanExpiredWarnings() {
        // 可以在这里实现清理逻辑
        // 删除超过一定时间的已处理预警记录
        logger.info("清理过期预警记录功能待实现");
    }
}

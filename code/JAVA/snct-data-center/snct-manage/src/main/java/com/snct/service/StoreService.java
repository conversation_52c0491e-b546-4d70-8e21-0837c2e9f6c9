package com.snct.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.*;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.commoncore.utils.DateUtils;
import com.snct.dctcore.commoncore.utils.GpsCoordinateUtils;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: StoreService
 * @Description: 存储服务
 * @author: wzewei
 * @date: 2025-08-12 09:16
 */
@Component
public class StoreService {

    public final static Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 抽稀的分钟集合
     */
    private static final Integer[] INTERVALS = {0, 1, 5, 15};

    /**
     * 保存到hbase中
     *
     * @param message
     */
    void save2Hbase(KafkaMessage message) {
        logger.info("Hbase需要保存数据--，{}", JSONObject.toJSONString(message));
        try {
            Class cl = getClassByType(message.getType());
            if (cl == null) {
                return;
            }
            Object hbaseVo = JSON.parseObject(message.getMsg(), cl);

            // 保存最全数据
            save2Hbase(message, hbaseVo, cl, 100);

            // 抽稀并保存
            //Object thinningVo = thinningData(message, hbaseVo);
            //Arrays.stream(INTERVALS).forEach(i -> save2Hbase(message, thinningVo, cl, i));
        } catch (Exception e) {
            logger.error("数据异常", e);
        }
    }

    /**
     * 保存到hbase中 判断数据是否需要抽稀，并保存
     *
     * @param message
     * @param hbaseVo
     * @param interval 时间间隔
     */
    private void save2Hbase(KafkaMessage message, Object hbaseVo, Class cl, Integer interval) {

        Long nTime = message.getInitialTime();
        // 把时间转换为往前最近的整n分钟
        if (interval != 0 && interval != 100) {
            nTime = DateUtils.fetchCompleteNMinute(message.getInitialTime(), interval, 0);
        }

        String rowKey = hBaseDaoUtil.getRowKey(nTime);

        String typeStr = DeviceTypeEnum.getByValue(message.getType()).getAlias();

        //String key = RedisParameter.DEVICE_LATEST_TIME + "VACUUMING-" + interval + "-" + rowKey;
        //ValueOperations<Object, Object> valueOperations = redisTemplate.opsForValue();
        //if (valueOperations.get(key) != null) {
        //    return;
        //}

        setValue2Vo(hbaseVo, cl, "id", rowKey);
        setValue2Vo(hbaseVo, cl, "initialTime", String.valueOf(nTime));
        setValue2Vo(hbaseVo, cl, "initialBjTime", DateUtils.getDateToString(nTime));

        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(message.getSn(), typeStr, message.getCode(), interval), hbaseVo);

        // 保存最新数据到redis
        saveLatestDataToRedis(message, hbaseVo);

        // 更新船只位置缓存
        if (message.getType().equals(DeviceTypeEnum.GPS.getValue())) {
            saveShipLocation(message, (GpsHbaseVo) hbaseVo);
        }

        // 设置过期时间为1天
        //valueOperations.set(key, nTime, 1, TimeUnit.DAYS);
    }


    /**
     * 通过配置抽稀需要传输的数据
     *
     * @param message
     * @param vo
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     * @throws InstantiationException
     */
    private Object thinningData(KafkaMessage message, Object vo) throws IllegalAccessException, NoSuchFieldException,
            InstantiationException {
        //List<String> attributes = transferAttributeService.queryAttributesByCode(message.getCode());
        List<String> attributes = null;
        if (attributes == null || attributes.size() == 0) {
            return null;
        }

        Class cl = vo.getClass();
        Object thinningVo = cl.newInstance();

        for (String attr : attributes) {
            Field field = cl.getDeclaredField(attr);
            field.setAccessible(true);

            if (field.get(vo) != null) {
                setValue2Vo(thinningVo, cl, attr, field.get(vo).toString());
            }
        }

        return thinningVo;
    }

    /**
     * 给hbaseVo字段赋值
     *
     * @param hbaseVo
     * @param cl
     * @param fieldName
     * @param value
     */
    private void setValue2Vo(Object hbaseVo, Class cl, String fieldName, String value) {
        Field field;
        try {
            field = cl.getDeclaredField(fieldName);
            field.setAccessible(true);

            field.set(hbaseVo, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            logger.error("字段设置错误---{}", e);
        }
    }

    /**
     * 根据类型获取 类的Class
     *
     * @param type
     * @return
     */
    public Class getClassByType(Integer type) {
        switch (DeviceTypeEnum.getByValue(type)) {
            case GPS: {
                return GpsHbaseVo.class;
            }
            case AWS: {
                return AwsHbaseVo.class;
            }
            case ATTITUDE: {
                return AttitudeHbaseVo.class;
            }
            case PDU: {
                return PduHbaseVo.class;
            }
            case MODEM: {
                return ModemHbaseVo.class;
            }
            case AMPLIFIER: {
                return AmplifierHbaseVo.class;
            }
            default: {
                return null;
            }
        }
    }

    /**
     * 保存设备最新数据到Redis中
     *
     * @param message Kafka消息
     * @param hbaseVo HBase数据对象
     */
    private void saveLatestDataToRedis(KafkaMessage message, Object hbaseVo) {
        try {
            String deviceKey = RedisParameter.DEVICE_DATA + message.getSn() + ":" + message.getCode();

            // 保存设备最新数据
            String dataJson = JSONObject.toJSONString(hbaseVo);
            redisTemplate.opsForValue().set(deviceKey, dataJson, 24, TimeUnit.HOURS);

        } catch (Exception e) {
            logger.error("保存设备最新数据到Redis失败 - 设备: {}, 代码: {}, 错误: {}",
                    message.getSn(), message.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 保存船只位置缓存
     *
     * @param message Kafka消息
     * @param gpsData GPS数据对象
     */
    private void saveShipLocation(KafkaMessage message, GpsHbaseVo gpsData) {
        try {
            // 验证GPS数据有效性
            if (StringUtils.isBlank(gpsData.getLongitude()) ||
                    StringUtils.isBlank(gpsData.getLatitude()) ||
                    "null".equals(gpsData.getLongitude()) ||
                    "null".equals(gpsData.getLatitude())) {
                return;
            }

            // 转换GPS坐标格式
            String convertedLongitude = GpsCoordinateUtils.convertLongitude(
                    gpsData.getLongitude(), gpsData.getLongitudeHemisphere());
            String convertedLatitude = GpsCoordinateUtils.convertLatitude(
                    gpsData.getLatitude(), gpsData.getLatitudeHemisphere());

            // 验证转换后的坐标
            if (!GpsCoordinateUtils.isValidCoordinate(convertedLongitude, convertedLatitude)) {
                return;
            }

            Map<String, Object> locationData = new HashMap<>();
            locationData.put("longitude", convertedLongitude);
            locationData.put("latitude", convertedLatitude);
            //locationData.put("originalLongitude", gpsData.getLongitude());
            //locationData.put("originalLatitude", gpsData.getLatitude());
            locationData.put("longitudeHemisphere", gpsData.getLongitudeHemisphere());
            locationData.put("latitudeHemisphere", gpsData.getLatitudeHemisphere());
            locationData.put("timestamp", message.getInitialTime());
            locationData.put("speed", gpsData.getGroundRateKm());
            locationData.put("course", gpsData.getRightNorthCourse());
            locationData.put("updateTime", DateUtils.getDateToString(message.getInitialTime()));

            // 更新单船只位置缓存
            String shipLocationKey = RedisParameter.SHIP_LOCATION_LATEST + message.getSn();
            redisTemplate.opsForValue().set(shipLocationKey, locationData, 24, TimeUnit.HOURS);

            // 更新所有船只位置集合
            String allShipsKey = RedisParameter.SHIP_LOCATION_ALL;
            redisTemplate.opsForHash().put(allShipsKey, message.getSn(), JSONObject.toJSONString(locationData));

            // 更新地理位置索引（用于距离计算）
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;
            redisTemplate.opsForGeo().add(geoKey,
                    new Point(Double.valueOf(convertedLongitude), Double.valueOf(convertedLatitude)),
                    message.getSn());

        } catch (Exception e) {
            logger.error("更新船只位置缓存失败: sn={}, 原始坐标=({}, {}), 错误信息: {}",
                    message.getSn(), gpsData.getLongitude(), gpsData.getLatitude(), e.getMessage(), e);
        }
    }
}

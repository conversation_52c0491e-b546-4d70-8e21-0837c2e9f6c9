package com.snct.typhoon.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.utils.DateUtils;
import com.snct.typhoon.domain.*;
import com.snct.typhoon.domain.vo.*;
import com.snct.typhoon.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: TyphoonService
 * @Description: 台风相关服务
 * @author: wzewei
 * @date: 2025-08-19 16:44:14
 */
@Service
public class TyphoonService {

    private static final Logger logger = LoggerFactory.getLogger(TyphoonService.class);

    @Autowired
    private TyphoonMapper typhoonMapper;
    @Autowired
    private TyphoonLandMapper typhoonLandMapper;
    @Autowired
    private TyphoonPointMapper typhoonPointMapper;
    @Autowired
    private TyphoonForecastMapper typhoonForecastMapper;
    @Autowired
    private TyphoonForecastPointMapper typhoonForecastPointMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private TyphoonWarningService typhoonWarningService;

    public void obtainTyphoon() {
        // 获取当前年份的台风列表
        obtainTyphoonList();
        // 获取台风路径信息
        obtainTyphoonInfo();
    }

    /**
     * 获取台风列表
     *
     * @return
     */
    private void obtainTyphoonList() {
        try {
            // 获取当前的年份
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            int year = cal.get(Calendar.YEAR);

            String url = "https://typhoon.slt.zj.gov.cn/Api/TyphoonList/" + year;
            //发送GET请求
            String getBack = sendGet(url, null, null);
            getBack = getBack.trim();
            logger.info("获取台风信息----{}", getBack);
            if (StringUtils.isBlank(getBack)) {
                return;
            }
            List<TyphoonVo> list = JSONObject.parseArray(getBack, TyphoonVo.class);
            // 更新redis
            ValueOperations<String, List<TyphoonVo>> opsForValue = redisTemplate.opsForValue();
            opsForValue.set(RedisParameter.TYPHOON_LIST + year, list);

            // 保存台风信息
            //saveTyphoon(list, year);

            Thread.sleep(2000);
        } catch (Exception e) {
            logger.error("获取台风信息出错---{}", e);
        }
    }

    /**
     * 保存台风信息
     *
     * @param typhoonList
     * @param year
     */
    private void saveTyphoon(List<TyphoonVo> typhoonList, Integer year) {

        for (TyphoonVo typhoonVo : typhoonList) {
            Typhoon typhoonSelect = new Typhoon();
            typhoonSelect.setTfid(typhoonVo.getTfid());
            Typhoon typhoon = typhoonMapper.selectTyphoon(typhoonSelect);
            if (typhoon == null) {
                Typhoon newTyphoon = TyphoonVo.voToObj(typhoonVo);
                newTyphoon.setStartTimeStamp(DateUtils.parseDate(typhoonVo.getStarttime(), "yyyy-MM-dd HH:mm:ss").getTime());
                newTyphoon.setEndTimeStamp(DateUtils.parseDate(typhoonVo.getEndtime(), "yyyy-MM-dd HH:mm:ss").getTime());
                newTyphoon.setYear(year);
                typhoonMapper.addTyphoon(newTyphoon);
            } else if ("1".equals(typhoonVo.getIsactive())) {
                Typhoon newTyphoon = TyphoonVo.voToObj(typhoonVo);
                newTyphoon.setId(typhoon.getId());
                newTyphoon.setStartTimeStamp(DateUtils.parseDate(typhoonVo.getStarttime(), "yyyy-MM-dd HH:mm:ss").getTime());
                newTyphoon.setEndTimeStamp(DateUtils.parseDate(typhoonVo.getEndtime(), "yyyy-MM-dd HH:mm:ss").getTime());
                newTyphoon.setYear(year);
                typhoonMapper.updateTyphoon(newTyphoon);
            }
        }
    }

    /**
     * 获取台风的路径信息
     *
     * @return
     */
    private void obtainTyphoonInfo() {
        try {
            // 获取当前的年份
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            int year = cal.get(Calendar.YEAR);

            ValueOperations<String, List<TyphoonVo>> opsForValue1 = redisTemplate.opsForValue();
            ValueOperations<String, List<TyphoonInfoVo>> opsForValue2 = redisTemplate.opsForValue();
            List<TyphoonVo> typhoonList = opsForValue1.get(RedisParameter.TYPHOON_LIST + year);
            if (typhoonList == null) {
                return;
            }

            // 用于收集活跃台风信息
            List<ActiveTyphoonSummary> activeTyphoonSummaries = new ArrayList<>();

            for (TyphoonVo typhoon : typhoonList) {

                // 历史台风，已经保存过就不需要再次获取数据
                if ("0".equals(typhoon.getIsactive()) && opsForValue2.get(RedisParameter.TYPHOON_INFO + typhoon.getTfid()) != null) {
                    continue;
                }

                String url = "https://typhoon.slt.zj.gov.cn/Api/TyphoonInfo/" + typhoon.getTfid();
                String getBack = sendGet(url, null, null);
                getBack = getBack.trim();
                if (StringUtils.isBlank(getBack)) {
                    continue;
                }
                List<TyphoonInfoVo> typhoonInfoVoList = JSONObject.parseArray(getBack, TyphoonInfoVo.class);
                if (typhoonInfoVoList.size() == 0) {
                    continue;
                }
                // 更新redis
                opsForValue2.set(RedisParameter.TYPHOON_INFO + typhoon.getTfid(), typhoonInfoVoList);

                // 如果是活跃台风，提取核心信息
                if ("1".equals(typhoon.getIsactive())) {
                    ActiveTyphoonSummary summary = extractActiveTyphoonSummary(typhoon, typhoonInfoVoList.get(0));
                    if (summary != null) {
                        activeTyphoonSummaries.add(summary);
                    }
                }

                // 保存台风登录地
                //saveTyphoonLand(typhoonInfoVoList.get(0));
                // 保存台风路径点位
                //saveTyphoonPoint(typhoonInfoVoList.get(0));
                logger.info("获取台风路径点位信息----{}", typhoon.getTfid());

                Thread.sleep(2000);
            }

            // 保存活跃台风摘要信息
            saveActiveTyphoonSummary(activeTyphoonSummaries);

            // 执行台风预警检查并保存
            if (!activeTyphoons.isEmpty()) {
                logger.info("发现 {} 个活跃台风，开始执行预警检查", activeTyphoons.size());
                typhoonWarningService.checkAndSaveTyphoonWarnings();
            }

        } catch (Exception e) {
            logger.error("获取台风信息出错--{}", e);
        }
    }

    /**
     * 保存台风登录地
     *
     * @param typhoonInfoVo
     */
    private void saveTyphoonLand(TyphoonInfoVo typhoonInfoVo) {
        if (typhoonInfoVo.getLand().size() == 0) {
            return;
        }

        TyphoonLand typhoonLandSelect = new TyphoonLand();
        typhoonLandSelect.setTfid(typhoonInfoVo.getTfid());
        List<TyphoonLand> typhoonLandList = typhoonLandMapper.selectTyphoonLandList(typhoonLandSelect);

        TyphoonLandVo typhoonLandVo = typhoonInfoVo.getLand().get(0);

        // 没有保存则保存，保存过则更新最新消息
        if (typhoonLandList.size() == 0) {
            TyphoonLand typhoonLand = TyphoonLandVo.voToObj(typhoonLandVo);
            typhoonLand.setTfid(typhoonInfoVo.getTfid());
            typhoonLandMapper.addTyphoonLand(typhoonLand);
        } else {
            TyphoonLand typhoonLand = TyphoonLandVo.voToObj(typhoonLandVo);
            typhoonLand.setId(typhoonLandList.get(0).getId());
            typhoonLand.setTfid(typhoonInfoVo.getTfid());
            typhoonLandMapper.updateTyphoonLand(typhoonLand);
        }
    }

    /**
     * 保存台风路径点位
     *
     * @param typhoonInfoVo
     */
    private void saveTyphoonPoint(TyphoonInfoVo typhoonInfoVo) {
        // 保存台风路径点位
        if (typhoonInfoVo.getPoints().size() == 0) {
            return;
        }

        TyphoonPoint typhoonPointSelect = new TyphoonPoint();
        typhoonPointSelect.setTfid(typhoonInfoVo.getTfid());
        List<TyphoonPoint> typhoonPointList = typhoonPointMapper.selectTyphoonPointList(typhoonPointSelect);

        for (TyphoonPointVo typhoonPointVo : typhoonInfoVo.getPoints()) {
            boolean exist = false;
            for (TyphoonPoint sourcePoint : typhoonPointList) {
                // 已经保存过的不需要再保存
                if (typhoonPointVo.getLat().equals(sourcePoint.getLat()) && typhoonPointVo.getLng().equals(sourcePoint.getLng())) {
                    exist = true;
                    break;
                }
            }
            // 未保存则开始保存
            if (!exist) {
                TyphoonPoint typhoonPoint = TyphoonPointVo.voToObj(typhoonPointVo);
                typhoonPoint.setTfid(typhoonInfoVo.getTfid());
                typhoonPoint.setTimeStamp(DateUtils.parseDate(typhoonPointVo.getTime(), "yyyy-MM-dd HH:mm:ss").getTime());
                typhoonPointMapper.addTyphoonPoint(typhoonPoint);

                //保存预测
                saveTyphoonForecast(typhoonPoint);
            }
        }
    }

    /**
     * 保存台风预测
     *
     * @param typhoonPoint
     */
    private void saveTyphoonForecast(TyphoonPoint typhoonPoint) {
        for (TyphoonForecast typhoonForecast : typhoonPoint.getForecast()) {

            typhoonForecast.setTfid(typhoonPoint.getTfid());
            typhoonForecast.setTfid(typhoonPoint.getTfid());
            typhoonForecast.setTyphoonPointId(typhoonPoint.getId());
            typhoonForecastMapper.addTyphoonForecast(typhoonForecast);

            // 保存预测的路径点位
            saveTyphoonForecastPoint(typhoonForecast);
        }
    }

    /**
     * 保存台风预测中路径的点位
     *
     * @param typhoonForecast
     */
    private void saveTyphoonForecastPoint(TyphoonForecast typhoonForecast) {
        for (TyphoonForecastPoint typhoonForecastPoint : typhoonForecast.getForecastPoints()) {
            typhoonForecastPoint.setTfid(typhoonForecast.getTfid());
            typhoonForecastPoint.setTyphoonForecastId(typhoonForecast.getId());
            typhoonForecastPoint.setTimeStamp(DateUtils.parseDate(typhoonForecastPoint.getTime(), "yyyy-MM-dd " +
                    "HH:mm:ss").getTime());
            typhoonForecastPointMapper.addTyphoonForecastPoint(typhoonForecastPoint);
        }
    }

    /**
     * get方法
     *
     * @param url
     * @param param
     * @param header
     * @return
     */
    private String sendGet(String url, String param, Map<String, String> header) {
        String result = "";
        try {
            BufferedReader in = null;
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            //设置超时时间
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(15000);
            // 设置通用的请求属性
            if (header != null) {
                Iterator<Map.Entry<String, String>> it = header.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, String> entry = it.next();
                    System.out.println(entry.getKey() + ":" + entry.getValue());
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" +
                    " (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36");

            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                //System.out.println(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应，设置utf8防止中文乱码
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            if (in != null) {
                in.close();
            }
        } catch (IOException ioe) {
            logger.error("Get获取失败，--{}", ioe);
        }
        return result;
    }

    /**
     * 提取活跃台风核心信息
     *
     * @param typhoon 台风基本信息
     * @param typhoonInfo 台风详细信息
     * @return 活跃台风摘要信息
     */
    private ActiveTyphoonSummary extractActiveTyphoonSummary(TyphoonVo typhoon, TyphoonInfoVo typhoonInfo) {
        try {
            ActiveTyphoonSummary summary = new ActiveTyphoonSummary();
            summary.setTfid(typhoon.getTfid());
            summary.setName(typhoon.getName());

            // 获取最新位置信息
            if (typhoonInfo.getPoints() != null && !typhoonInfo.getPoints().isEmpty()) {
                // 取最后一个点作为最新位置
                TyphoonPointVo latestPoint = typhoonInfo.getPoints().get(typhoonInfo.getPoints().size() - 1);
                summary.setCenterLat(latestPoint.getLat());
                summary.setCenterLng(latestPoint.getLng());
                summary.setStrong(latestPoint.getStrong());
                summary.setPower(latestPoint.getPower());
                summary.setPressure(latestPoint.getPressure());
                summary.setSpeed(latestPoint.getSpeed());
                summary.setMovedirection(latestPoint.getMovedirection());
                summary.setMovespeed(latestPoint.getMovespeed());
                summary.setUpdateTime(latestPoint.getTime());
            } else {
                // 如果没有点位信息，使用基本信息
                summary.setCenterLat(typhoonInfo.getCenterlat());
                summary.setCenterLng(typhoonInfo.getCenterlng());
                summary.setUpdateTime(typhoon.getEndtime());
            }

            return summary;
        } catch (Exception e) {
            logger.error("提取台风{}核心信息失败", typhoon.getTfid(), e);
            return null;
        }
    }

    /**
     * 保存活跃台风摘要信息
     *
     * @param activeTyphoons 活跃台风列表
     */
    private void saveActiveTyphoonSummary(List<ActiveTyphoonSummary> activeTyphoons) {
        try {
            if (activeTyphoons.isEmpty()) {
                // 如果没有活跃台风，清空缓存
                redisTemplate.delete(RedisParameter.TYPHOON_ACTIVE_SUMMARY);
                return;
            }

            Map<String, Object> activeTyphoonData = new HashMap<>();
            activeTyphoonData.put("updateTime", DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            activeTyphoonData.put("count", activeTyphoons.size());
            activeTyphoonData.put("typhoons", activeTyphoons);

            // 保存到Redis，一个小时过期
            redisTemplate.opsForValue().set(RedisParameter.TYPHOON_ACTIVE_SUMMARY, activeTyphoonData, 60, TimeUnit.MINUTES);

        } catch (Exception e) {
            logger.error("保存活跃台风摘要信息失败", e);
        }
    }

    /**
     * 获取活跃台风摘要信息
     *
     * @return 活跃台风摘要数据
     */
    public Map<String, Object> getActiveTyphoonSummary() {
        try {
            Map<String, Object> data = (Map<String, Object>) redisTemplate.opsForValue().get(RedisParameter.TYPHOON_ACTIVE_SUMMARY);
            return data != null ? data : new HashMap<>();
        } catch (Exception e) {
            return new HashMap<>();
        }
    }
}
